<script setup lang="ts">
// 方案互动-礼品方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits, computed } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import GiftSelectionModal from '@haierbusiness-front/components/mice/giftSelectionModal/index.vue';

import dayjs, { Dayjs } from 'dayjs';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  schemeDetail: {
    type: Object as () => any,
    default: () => ({}),
  },
});

const emit = defineEmits(['presentPriceEmit', 'schemePresentEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

// 计算属性：获取要显示的礼品数据
const displayNewSchemeList = computed(() => {
  if (props.readonly && props.schemeDetail?.presents) {
    // 查看模式：转换数据格式以匹配组件期望
    const transformedData = props.schemeDetail.presents.map((item: any, index: number) => ({
      ...item,
      // 确保必要字段存在
      miceDemandPresentId: item.miceDemandPresentId || item.id,
      miceSchemePresentId: item.id || item.miceSchemePresentId,
      optionType: item.optionType || 0,
      demandTotalPrice: item.demandTotalPrice || 0,
      schemeTotalPrice: item.schemeTotalPrice || item.demandTotalPrice || 0,
      billTotalPrice: item.billTotalPrice || item.schemeTotalPrice || item.demandTotalPrice || 0,
      description: item.description || '',
      presentDetails: item.presentDetails || [{
        miceDemandPresentId: item.miceDemandPresentId || item.id,
        deliveryDate: item.deliveryDate || '-',
        schemePersonNum: item.schemePersonNum || item.personNum || 0,
        billPersonNum: item.billPersonNum || item.schemePersonNum || item.personNum || 0,
        unitPrice: item.unitPrice || 0,
        schemeUnitPrice: item.schemeUnitPrice || item.unitPrice || 0,
        billUnitPrice: item.billUnitPrice || item.schemeUnitPrice || item.unitPrice || 0,
        productId: item.productId || '',
        productMerchantId: item.productMerchantId || '',
        productName: item.productName || '',
        unit: item.unit || '',
        personSpecs: item.personSpecs || '',
      }],
    }));

    return transformedData;
  } else {
    // 编辑模式：使用 newSchemeList
    return newSchemeList.value || [];
  }
});

// 计算属性：获取要显示的原始需求数据
const displayOldSchemeList = computed(() => {
  if (props.readonly && props.schemeDetail?.presents) {
    // 查看模式：从 schemeDetail 中提取原始需求数据用于左侧显示
    return props.schemeDetail.presents.map((item: any) => {
      const presentDetail = item.presentDetails?.[0] || {};
      return {
        productName: presentDetail.productName || item.productName || '-',
        personNum: presentDetail.schemePersonNum || item.personNum || 0,
        unit: presentDetail.unit || item.unit || '-',
        demandTotalPrice: item.demandTotalPrice || 0,
        deliveryDate: presentDetail.deliveryDate || item.deliveryDate || '-',
        personSpecs: presentDetail.personSpecs || item.personSpecs || '-',
        id: item.id || item.miceDemandPresentId,
        miceDemandPresentId: item.miceDemandPresentId || item.id,
      };
    });
  } else {
    // 编辑模式：使用 oldSchemeList
    return oldSchemeList.value || [];
  }
});

const subtotal = ref<number>(0); // 小计

const isVerifyFailed = ref<boolean>(false); // 校验是否失败
const showGiftModal = ref(false);
const currentGiftIndex = ref(0);
const giftModes = ref<Record<number, 'select' | 'manual'>>({});

// 礼品需求表单
const formState = reactive<any>({
  // 礼品需求
  presents: [],
});
const currentScheme = reactive<any>({});
// 选择礼品回调
const handleSelectGift = (gift: any, index: number) => {
  console.log(gift);
  console.log(currentScheme);
  newSchemeList.value.forEach((curItem, curIndex) => {
    if (currentScheme.miceDemandPresentId === curItem.miceDemandPresentId) {
      curItem.presentDetails[0].productId = gift.id;
      curItem.presentDetails[0].productMerchantId = gift.merchantId;
      curItem.presentDetails[0].productName = gift.presentName;
      curItem.presentDetails[0].personSpecs = gift.specComb;
      curItem.presentDetails[0].unitPrice = gift.salePrice;
      // 移除自动计算总预算的逻辑，保持用户原有的总预算不变
    }
  });
  changePrice();
};
const changeMode = (val, item) => {
  if (val == 1) {
    Object.assign(currentScheme, item), (showGiftModal.value = true);
  }
};
const getPrice = (item) => {
  // 查看模式不需要计算价格
  if (props.readonly) {
    return;
  }
  // 移除自动计算逻辑，数量和总预算独立
  changePrice();
};
const changePrice = () => {
  // 查看模式下计算小计但不发送事件
  if (props.readonly) {
    calculateSubtotalForReadonly();
    return;
  }

  // 小计
  subtotal.value = 0;
  newSchemeList.value.forEach((e) => {
    // 账单上传模式使用 billTotalPrice，其他模式使用 schemeTotalPrice
    const priceField = props.schemeType === 'billUpload' ? e.billTotalPrice : e.schemeTotalPrice;
    if (priceField) {
      subtotal.value += priceField;
    }
  });

  emit('presentPriceEmit', subtotal.value);
};

// 查看模式下的小计计算
const calculateSubtotalForReadonly = () => {
  subtotal.value = 0;
  displayNewSchemeList.value.forEach((e) => {
    // 账单上传模式使用 billTotalPrice，其他模式使用 schemeTotalPrice
    const priceField = props.schemeType === 'billUpload' ? e.billTotalPrice : e.schemeTotalPrice;
    if (priceField) {
      subtotal.value += priceField;
    }
  });

  // 查看模式下也需要发送价格数据给父组件，用于计算总合计
  emit('presentPriceEmit', subtotal.value);
};

// 账单总额变化处理函数（仅在账单上传模式下使用）
const changeBillTotalPrice = (index: number) => {
  // 查看模式不需要处理价格变化
  if (props.readonly) {
    return;
  }

  console.log('%c [ 账单总额变化 ]', 'font-size:13px; background:orange; color:white;',
    `索引: ${index}, schemeTotalPrice: ${newSchemeList.value[index].schemeTotalPrice}, billTotalPrice: ${newSchemeList.value[index].billTotalPrice}`);

  // 重新计算小计
  changePrice();
};

watch(
  () => [props.demandInfo, props.schemeCacheInfo, props.schemeDetail],
  () => {
    // 查看模式：直接显示数据，不走缓存逻辑，但需要计算小计
    if (props.readonly) {
      // 延迟计算小计，确保计算属性已更新
      nextTick(() => {
        calculateSubtotalForReadonly();
      });
      return;
    }

    // 从方案数据中提取原始需求数据用于左侧显示
    const rawPresents = JSON.parse(JSON.stringify(props.demandInfo))?.presents || [];
    oldSchemeList.value = rawPresents.map((item: any) => {
      const presentDetail = item.presentDetails?.[0] || {};
      return {
        // 左侧显示的原始需求数据字段
        productName: presentDetail.productName || '-',
        personNum: presentDetail.schemePersonNum || 0, // 使用方案数量作为原始数量显示
        unit: presentDetail.unit || '-',
        demandTotalPrice: item.demandTotalPrice || 0,
        deliveryDate: presentDetail.deliveryDate || '-',
        personSpecs: presentDetail.personSpecs || '-',
        // 保留原始字段以备用
        id: item.id,
        miceDemandPresentId: item.miceDemandPresentId,
      };
    });

    if (props.isSchemeCache && props.schemeCacheInfo) {
      // 缓存 - 反显
      const cacheList = props.schemeCacheInfo?.presents || [];
      newSchemeList.value = cacheList.map((item: any) => {
        // 为账单上传模式添加必需字段
        const baseItem: any = {
          ...item,
          optionType: item.optionType || 0,
          // 确保关键字段正确映射
          miceDemandPresentId: item.miceDemandPresentId || item.presentDetails?.[0]?.miceDemandPresentId,
          miceSchemePresentId: item.id, // 方案礼品id
        };

        // 如果缓存中没有sourceId或为null，从原始需求数据中获取
        if (!baseItem.hasOwnProperty('sourceId') || baseItem.sourceId === null) {
          const originalItem = props.demandInfo?.presents?.find(
            (demandItem: any) => demandItem.id === baseItem.miceDemandPresentId,
          );
          baseItem.sourceId = originalItem?.sourceId || null;
        }

        if (props.schemeType === 'billUpload') {
          // 添加账单上传必需的字段
          baseItem.invoiceTempId = item.invoiceTempId;
          baseItem.statementTempId = item.statementTempId;

          // 重要：schemeTotalPrice 必须使用详情返回的原始值，不能使用缓存中的值
          const originalItem = props.demandInfo?.presents?.find((demandItem: any) => demandItem.id === item.miceDemandPresentId);
          if (originalItem) {
            baseItem.schemeTotalPrice = originalItem.schemeTotalPrice;
          }

          // billTotalPrice 独立初始化，用户可以修改（确保是独立的数值，不是引用）
          if (!item.hasOwnProperty('billTotalPrice') || item.billTotalPrice === null || item.billTotalPrice === undefined) {
            baseItem.billTotalPrice = Number(baseItem.schemeTotalPrice || baseItem.demandTotalPrice || 0);
          } else {
            baseItem.billTotalPrice = Number(item.billTotalPrice);
          }

          console.log('%c [ 礼品组件初始化 ]', 'font-size:13px; background:blue; color:white;',
            `ID: ${item.miceDemandPresentId}, schemeTotalPrice: ${baseItem.schemeTotalPrice}, billTotalPrice: ${baseItem.billTotalPrice}`);

          baseItem.miceBillAttachmentInvoiceId = item.miceBillAttachmentInvoiceId || null;
          baseItem.miceBillAttachmentStatementId = item.miceBillAttachmentStatementId || null;

          // 为presentDetails添加账单字段，完全保留原有数据
          if (baseItem.presentDetails && baseItem.presentDetails.length > 0) {
            baseItem.presentDetails = baseItem.presentDetails.map((detail: any) => {
              // 创建新对象，确保不丢失任何字段
              const processedDetail = {
                ...detail, // 保留所有原有字段，包括sourceId
                billUnitPrice:
                  detail.billUnitPrice !== null
                    ? detail.billUnitPrice
                    : detail.schemeUnitPrice !== null
                    ? detail.schemeUnitPrice
                    : detail.unitPrice !== null
                    ? detail.unitPrice
                    : null,
                billPersonNum: detail.billPersonNum || detail.schemePersonNum || null,
              };

              // 如果缓存中presentDetails没有sourceId或为null，从原始需求数据中获取
              if (!processedDetail.hasOwnProperty('sourceId') || processedDetail.sourceId === null) {
                // 直接在所有原始数据的presentDetails中查找匹配的sourceId
                let foundSourceId = null;
                props.demandInfo?.presents?.forEach((demandItem: any) => {
                  demandItem.presentDetails?.forEach((origDetail: any) => {
                    if (origDetail.miceDemandPresentId === detail.miceDemandPresentId) {
                      foundSourceId = origDetail.sourceId;
                    }
                  });
                });
                processedDetail.sourceId = foundSourceId;
              }

              return processedDetail;
            });
          }
        }

        return baseItem;
      });

      // 左侧始终显示原始需求数据，不被方案数据覆盖
      // oldSchemeList.value 已在第91行正确设置为原始需求数据
      // 原始需求数据字段结构已经匹配模板期望的字段名称，无需额外映射
    } else {
      const demandData = JSON.parse(JSON.stringify(props.demandInfo))?.presents || [];
      newSchemeList.value = demandData.map((e: any) => {
        const baseItem: any = {
          miceDemandPresentId: e.id,
          demandTotalPrice: e.demandTotalPrice,
          schemeTotalPrice: e.demandTotalPrice,
          optionType: e.optionType,
          description: e.description,
          sourceId: e.sourceId || null, // 从原始数据中获取sourceId
          presentDetails: [
            {
              miceDemandPresentId: e.id,
              deliveryDate: e.deliveryDate,
              schemePersonNum: e.personNum,
              unitPrice: e.unitPrice,
              productId: e.productId,
              productMerchantId: e.productMerchantId,
              productName: e.productName,
              unit: e.unit,
              personSpecs: e.personSpecs,
              sourceId: e.presentDetails?.[0]?.sourceId || null, // 从原始数据中获取sourceId
            },
          ],
        };

        // 为账单上传模式添加必需字段
        if (props.schemeType === 'billUpload') {
          baseItem.invoiceTempId = Date.now() + Math.random();
          baseItem.statementTempId = Date.now() + Math.random();
          // 确保 billTotalPrice 是独立的数值，不是引用
          baseItem.billTotalPrice = Number(e.demandTotalPrice || 0);
          baseItem.miceBillAttachmentInvoiceId = null;
          baseItem.miceBillAttachmentStatementId = null;

          console.log('%c [ 礼品组件新建 ]', 'font-size:13px; background:green; color:white;',
            `ID: ${e.id}, schemeTotalPrice: ${baseItem.schemeTotalPrice}, billTotalPrice: ${baseItem.billTotalPrice}`);

          // 为presentDetails添加账单字段
          baseItem.presentDetails[0].billUnitPrice =
            baseItem.presentDetails[0].schemeUnitPrice !== null
              ? baseItem.presentDetails[0].schemeUnitPrice
              : baseItem.presentDetails[0].unitPrice !== null
              ? baseItem.presentDetails[0].unitPrice
              : null;
          baseItem.presentDetails[0].billPersonNum = baseItem.presentDetails[0].schemePersonNum || 0;
        }

        return baseItem;
      });
    }

    // 小计
    changePrice();
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['礼品类型', '数量', '单位', '总预算', '送达日期', '礼品描述'];

// 不可选日期
const disabledDate = (current: Dayjs) => {
  // 当前日期之前的日期都禁用
  return current && current < dayjs().endOf('day');
};

// 暂存
const presentTempSave = () => {
  // 查看模式不需要暂存
  if (props.readonly) {
    return;
  }

  // 为账单上传模式处理数据结构
  const processedData =
    props.schemeType === 'billUpload'
      ? newSchemeList.value.map((item: any) => ({
          ...item,
          // 确保账单字段存在
          invoiceTempId: item.invoiceTempId,
          statementTempId: item.statementTempId,
          billTotalPrice: item.billTotalPrice || item.schemeTotalPrice || 0,
          presentDetails:
            item.presentDetails?.map((detail: any) => {
              // 完全保留原有字段，只添加账单字段
              return {
                ...detail, // 保留所有原有字段，包括sourceId
                billUnitPrice:
                  detail.billUnitPrice !== null
                    ? detail.billUnitPrice
                    : detail.schemeUnitPrice !== null
                    ? detail.schemeUnitPrice
                    : detail.unitPrice !== null
                    ? detail.unitPrice
                    : null,
                billPersonNum: detail.billPersonNum || detail.schemePersonNum || 0,
              };
            }) || [],
        }))
      : [...newSchemeList.value];

  emit('schemePresentEmit', processedData);
};
// 校验
const presentSub = () => {
  // 查看模式不需要校验
  if (props.readonly) {
    return true;
  }

  let isVerPassed = true;

  newSchemeList.value.forEach((e: any, i: number) => {
    isVerifyFailed.value = true;

    if (isVerPassed === false) return;

    if (!e.presentDetails || e.presentDetails.length === 0) {
      isVerPassed = false;
      return;
    }

    const presentDetail = e.presentDetails[0] || {};

    if (!presentDetail.productName) {
      message.error('请输入礼品方案' + (i + 1) + ' 礼品类型');

      isVerPassed = false;
      return;
    }
    // 根据模式校验不同的数量字段
    const personNumToCheck =
      props.schemeType === 'billUpload' ? presentDetail.billPersonNum : presentDetail.schemePersonNum;
    if (!personNumToCheck) {
      message.error('请输入礼品方案' + (i + 1) + ' 数量');

      isVerPassed = false;
      return;
    }
    if (!presentDetail.unit) {
      message.error('请输入礼品方案' + (i + 1) + ' 单位');

      isVerPassed = false;
      return;
    }

    // 账单上传模式检查 billTotalPrice，其他模式检查 schemeTotalPrice
    const priceField = props.schemeType === 'billUpload' ? e.billTotalPrice : e.schemeTotalPrice;
    const fieldName = props.schemeType === 'billUpload' ? '账单总额' : '总预算';

    if (!priceField) {
      message.error('请输入礼品方案' + (i + 1) + ' ' + fieldName);

      isVerPassed = false;
      return;
    }

    if (!presentDetail.deliveryDate) {
      message.error('请输入礼品方案' + (i + 1) + ' 送达日期');

      isVerPassed = false;
      return;
    }

    if (!presentDetail.personSpecs) {
      message.error('请输入礼品方案' + (i + 1) + ' 礼品描述');

      isVerPassed = false;
      return;
    }
  });

  if (isVerPassed) {
    presentTempSave();
  }

  return isVerPassed;
};

defineExpose({ presentSub, presentTempSave });

onMounted(async () => {
  // 查看模式下初始化小计
  if (props.readonly) {
    nextTick(() => {
      calculateSubtotalForReadonly();
    });
  }
});
</script>

<template>
  <!-- 礼品方案 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>礼品方案</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l">
        <div class="scheme_plan_table" v-for="(item, idx) in displayOldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '礼品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.productName || '-' }}
                </template>
                {{ item.productName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personNum || '-' }}
                </template>
                {{ item.personNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '-' }}
                </template>
                {{ item.unit || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandTotalPrice ? item.demandTotalPrice + '元' : '-' }}
                </template>
                {{ item.demandTotalPrice ? item.demandTotalPrice + '元' : '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.deliveryDate || '-' }}
                </template>
                {{ item.deliveryDate || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.personSpecs || '-' }}
                </template>
                {{ item.personSpecs || '-' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in displayNewSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '礼品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value">
              <div
                style="border: none"
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].productName ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    props.readonly ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    props.schemeType === 'billUpload'
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.productName || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.productName || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.productName">
                    {{ item.presentDetails[0]?.productName || '-' }}
                  </template>
                  <a-row>
                    <a-col :span="14" style="border-right: 1px solid #e5e6eb">
                      <a-input
                        @click="
                          item.optionType == 1 ? (Object.assign(currentScheme, item), (showGiftModal = true)) : ''
                        "
                        v-model:value="item.presentDetails[0].productName"
                        placeholder="礼品类型"
                        :maxlength="500"
                        :bordered="false"
                        allow-clear
                      />
                    </a-col>
                    <a-col :span="10" style="border: 1px solid #1868db; border-radius: 2px">
                      <a-select
                        class="select-mode"
                        @change="
                          (val: any) => {
                            changeMode(val, item);
                          }
                        "
                        v-model:value="item.optionType"
                      >
                        <a-select-option :value="0">手动</a-select-option>
                        <a-select-option :value="1">选择</a-select-option>
                      </a-select>
                    </a-col>
                  </a-row>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed &&
                  !(props.schemeType === 'billUpload'
                    ? item.presentDetails[0].billPersonNum
                    : item.presentDetails[0].schemePersonNum)
                    ? 'error_tip'
                    : '',
                ]"
              >
                <!-- 查看模式：显示方案数量 -->
                <div class="pl12" v-if="props.readonly || props.schemeType === 'biddingView' || props.schemeType === 'schemeView'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.schemePersonNum || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.schemePersonNum || '-' }}
                  </a-tooltip>
                </div>

                <!-- 账单上传模式：编辑账单数量 -->
                <a-tooltip placement="topLeft" v-else-if="props.schemeType === 'billUpload'">
                  <template #title v-if="item.presentDetails[0]?.billPersonNum">
                    {{ item.presentDetails[0]?.billPersonNum || '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.presentDetails[0].billPersonNum"
                    placeholder="请输入数量"
                    @blur="getPrice(item)"
                    :bordered="false"
                    :controls="false"
                    :min="1"
                    :max="99999"
                    style="width: calc(100% - 30px)"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>

                <!-- 编辑模式：编辑方案数量 -->
                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.schemePersonNum">
                    {{ item.presentDetails[0]?.schemePersonNum || '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.presentDetails[0].schemePersonNum"
                    placeholder="请输入数量"
                    @blur="getPrice(item)"
                    :bordered="false"
                    :controls="false"
                    :min="1"
                    :max="99999"
                    style="width: calc(100% - 30px)"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.presentDetails[0].unit ? 'error_tip' : '']"
              >
                <div
                  class="pl12"
                  v-if="
                    props.readonly ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    props.schemeType === 'billUpload'
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.unit || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.unit || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.unit">
                    {{ item.presentDetails[0]?.unit || '-' }}
                  </template>
                  <a-input
                    v-model:value="item.presentDetails[0].unit"
                    style="width: calc(100% - 30px)"
                    placeholder="请输入单位"
                    :maxlength="50"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <!-- 账单上传模式的价格字段处理 -->
              <div
                v-if="props.schemeType === 'billUpload'"
                :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.billTotalPrice ? 'error_tip' : '']"
              >
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.billTotalPrice">
                    {{ item.billTotalPrice ? item.billTotalPrice + '元' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.billTotalPrice"
                    @blur="changeBillTotalPrice(idx)"
                    placeholder="请输入账单总额"
                    :bordered="false"
                    :controls="false"
                    :min="0.01"
                    :max="999999.99"
                    :precision="2"
                    style="width: calc(100% - 30px)"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>

              <!-- 其他模式的价格字段处理 -->
              <div
                v-else
                :class="['scheme_plan_border', 'p0', isVerifyFailed && !item.schemeTotalPrice ? 'error_tip' : '']"
              >
                <div class="pl12" v-if="props.readonly || props.schemeType === 'biddingView' || props.schemeType === 'schemeView'">
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                    </template>
                    {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.schemeTotalPrice">
                    {{ item.schemeTotalPrice ? item.schemeTotalPrice + '元' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.schemeTotalPrice"
                    @blur="changePrice()"
                    placeholder="请输入总预算"
                    :bordered="false"
                    :controls="false"
                    :min="0.01"
                    :max="item.demandTotalPrice"
                    :precision="2"
                    style="width: calc(100% - 30px)"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].deliveryDate ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    props.readonly ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    props.schemeType === 'billUpload'
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.deliveryDate || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.deliveryDate || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.deliveryDate">
                    {{ item.presentDetails[0]?.deliveryDate || '-' }}
                  </template>
                  <a-date-picker
                    v-model:value="item.presentDetails[0].deliveryDate"
                    :disabledDate="disabledDate"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                    style="width: 100%"
                    :bordered="false"
                    :allow-clear="false"
                  />
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value">
              <div
                :class="[
                  'scheme_plan_border',
                  'p0',
                  isVerifyFailed && !item.presentDetails[0].personSpecs ? 'error_tip' : '',
                ]"
              >
                <div
                  class="pl12"
                  v-if="
                    props.readonly ||
                    props.schemeType === 'biddingView' ||
                    props.schemeType === 'schemeView' ||
                    props.schemeType === 'billUpload'
                  "
                >
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ item.presentDetails[0]?.personSpecs || '-' }}
                    </template>
                    {{ item.presentDetails[0]?.personSpecs || '-' }}
                  </a-tooltip>
                </div>

                <a-tooltip placement="topLeft" v-else>
                  <template #title v-if="item.presentDetails[0]?.personSpecs">
                    {{ item.presentDetails[0]?.personSpecs || '-' }}
                  </template>
                  <a-input
                    v-model:value="item.presentDetails[0].personSpecs"
                    style="width: calc(100% - 30px)"
                    placeholder="礼品描述"
                    :maxlength="500"
                    :bordered="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                <!-- 账单上传模式显示 billTotalPrice -->
                <template v-if="props.schemeType === 'billUpload'">
                  {{ item.billTotalPrice ? '¥' + formatNumberThousands(item.billTotalPrice) : '-' }}
                </template>
                <!-- 其他模式显示 schemeTotalPrice -->
                <template v-else>
                  {{ item.schemeTotalPrice ? '¥' + formatNumberThousands(item.schemeTotalPrice) : '-' }}
                </template>
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <!-- 账单上传模式的提示文字 -->
              <div v-if="props.schemeType === 'billUpload' && item.billTotalPrice">
                {{ item.billTotalPrice + '(元/账单总额)' }}
              </div>
              <!-- 其他模式的提示文字 -->
              <div v-else-if="item.schemeTotalPrice">
                {{ item.schemeTotalPrice + '(元/总预算)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
    <GiftSelectionModal
      v-model:visible="showGiftModal"
      :current-gift-index="currentGiftIndex"
      @select-gift="handleSelectGift"
    />
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_value {
    position: relative;
    /* 住宿人数 */
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      /* padding: 0; */
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;
      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_price_value {
    :deep(.ant-input-number .ant-input-number-input) {
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
.modal-click {
  cursor: pointer;
}
:deep(.ant-select, .select-mode) {
  width: 100%;
  .ant-select-selector {
    border: none;
  }
  // position: absolute;
  // top: 8px;
  // left: 5px;
  width: 100%;
}
</style>
